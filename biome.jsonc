{
  "$schema": "https://biomejs.dev/schemas/2.0.0/schema.json",

  "assist": {
    "actions": {
      "source": {
        "organizeImports": "on",
        "useSortedAttributes": "on",
        "useSortedKeys": "on",
        "useSortedProperties": "on",
      },
    },
    "enabled": true,
  },

  "css": {
    "assist": {
      "enabled": true,
    },
    "formatter": {
      "enabled": true,
      "indentStyle": "space",
      "indentWidth": 2,
      "lineEnding": "lf",
      "lineWidth": 120,
      "quoteStyle": "single",
    },
    "linter": {
      "enabled": true,
    },
    "parser": {
      "allowWrongLineComments": true,
      "cssModules": true,
    },
  },

  "files": {
    "ignoreUnknown": true,
    "includes": ["**", "!**/node_modules/**", "!**/dist/**", "!**/.tmp/**"],
  },

  "formatter": {
    "attributePosition": "auto",
    "bracketSameLine": true,
    "bracketSpacing": true,
    "enabled": true,
    "expand": "auto",
    "formatWithErrors": true,
    "indentStyle": "space",
    "indentWidth": 2,
    "lineEnding": "lf",
    "lineWidth": 120,
    "useEditorconfig": false,
  },

  "javascript": {
    "assist": {
      "enabled": true,
    },
    "formatter": {
      "arrowParentheses": "asNeeded",
      "attributePosition": "auto",
      "bracketSameLine": true,
      "bracketSpacing": true,
      "enabled": true,
      "expand": "auto",
      "indentStyle": "space",
      "indentWidth": 2,
      "jsxQuoteStyle": "single",
      "lineEnding": "lf",
      "lineWidth": 120,
      "quoteProperties": "asNeeded",
      "quoteStyle": "single",
      "semicolons": "asNeeded",
      "trailingCommas": "all",
    },

    "globals": ["$", "_", "Deno", "Bun", "JSX", "Std", "React"],
    "jsxRuntime": "transparent",
    "linter": {
      "enabled": true,
    },
    "parser": {
      "jsxEverywhere": false,
      "unsafeParameterDecoratorsEnabled": true,
    },
  },

  "json": {
    "assist": {
      "enabled": true,
    },
    "formatter": {
      "bracketSpacing": true,
      "enabled": true,
      "expand": "auto",
      "indentStyle": "space",
      "indentWidth": 2,
      "lineEnding": "lf",
      "lineWidth": 120,
      "trailingCommas": "all",
    },
    "linter": {
      "enabled": true,
    },
    "parser": {
      "allowComments": true,
      "allowTrailingCommas": true,
    },
  },

  "linter": {
    "enabled": true,
    "rules": {
      "a11y": {
        "noAccessKey": "error",
        "noAriaHiddenOnFocusable": "warn",
        "noAriaUnsupportedElements": "error",
        "noAutofocus": "error",
        "noDistractingElements": "error",
        "noHeaderScope": "error",
        "noInteractiveElementToNoninteractiveRole": "error",
        "noLabelWithoutControl": "warn",
        "noNoninteractiveElementToInteractiveRole": "error",
        "noNoninteractiveTabindex": "error",
        "noPositiveTabindex": "error",
        "noRedundantAlt": "error",
        "noRedundantRoles": "error",
        "noStaticElementInteractions": "error",
        "noSvgWithoutTitle": "error",
        "useAltText": "error",
        "useAnchorContent": "error",
        "useAriaActivedescendantWithTabindex": "error",
        "useAriaPropsForRole": "error",
        "useAriaPropsSupportedByRole": "error",
        "useButtonType": "error",
        "useFocusableInteractive": "error",
        "useGenericFontNames": "error",
        "useHeadingContent": "error",
        "useHtmlLang": "error",
        "useIframeTitle": "error",
        "useKeyWithClickEvents": "error",
        "useKeyWithMouseEvents": "error",
        "useMediaCaption": "error",
        "useSemanticElements": "error",
        "useValidAnchor": "error",
        "useValidAriaProps": "error",
        "useValidAriaRole": "warn",
        "useValidAriaValues": "error",
        "useValidAutocomplete": "error",
        "useValidLang": "error",
      },

      "complexity": {
        "noAdjacentSpacesInRegex": "error",
        "noArguments": "error",
        "noBannedTypes": "error",
        "noCommaOperator": "error",
        "noEmptyTypeParameters": "error",
        "noExcessiveCognitiveComplexity": {
          "level": "error",
          "options": {
            "maxAllowedComplexity": 25,
          },
        },
        "noExcessiveNestedTestSuites": "error",
        "noExtraBooleanCast": "error",
        "noFlatMapIdentity": "error",
        "noForEach": "error",
        "noStaticOnlyClass": "error",
        "noThisInStatic": "error",
        "noUselessCatch": "error",
        "noUselessConstructor": "error",
        "noUselessContinue": "error",
        "noUselessEmptyExport": "error",
        "noUselessEscapeInRegex": "error",
        "noUselessFragments": "error",
        "noUselessLabel": "error",
        "noUselessLoneBlockStatements": "warn",
        "noUselessRename": "error",
        "noUselessStringConcat": "error",
        "noUselessStringRaw": "error",
        "noUselessSwitchCase": "error",
        "noUselessTernary": "error",
        "noUselessThisAlias": "error",
        "noUselessTypeConstraint": "error",
        "noUselessUndefinedInitialization": "warn",
        "noVoid": "off",
        "useArrowFunction": "error",
        "useDateNow": "error",
        "useFlatMap": "error",
        "useLiteralKeys": "error",
        "useNumericLiterals": "error",
        "useOptionalChain": "error",
        "useRegexLiterals": "error",
        "useSimpleNumberKeys": "error",
        "useSimplifiedLogicExpression": "error",
        "useWhile": "error",
      },

      "correctness": {
        "noChildrenProp": "warn",
        "noConstAssign": "error",
        "noConstantCondition": "error",
        "noConstantMathMinMaxClamp": "error",
        "noConstructorReturn": "error",
        "noEmptyCharacterClassInRegex": "off",
        "noEmptyPattern": "error",
        "noGlobalObjectCalls": "warn",
        "noInnerDeclarations": "warn",
        "noInvalidBuiltinInstantiation": "error",
        "noInvalidConstructorSuper": "error",
        "noInvalidDirectionInLinearGradient": "warn",
        "noInvalidGridAreas": "warn",
        "noInvalidPositionAtImportRule": "warn",
        "noInvalidUseBeforeDeclaration": "error",
        "noMissingVarFunction": "error",
        "noNodejsModules": "off",
        "noNonoctalDecimalEscape": "error",
        "noPrecisionLoss": "error",
        "noPrivateImports": "error",
        "noRenderReturnValue": "error",
        "noSelfAssign": "error",
        "noSetterReturn": "error",
        "noStringCaseMismatch": "warn",
        "noSwitchDeclarations": "error",
        "noUndeclaredDependencies": "off",
        "noUndeclaredVariables": "error",
        "noUnknownFunction": "error",
        "noUnknownMediaFeatureName": "error",
        "noUnknownProperty": "error",
        "noUnknownPseudoClass": "error",
        "noUnknownPseudoElement": "error",
        "noUnknownTypeSelector": "error",
        "noUnknownUnit": "error",
        "noUnmatchableAnbSelector": "error",
        "noUnreachable": "warn",
        "noUnreachableSuper": "error",
        "noUnsafeFinally": "error",
        "noUnsafeOptionalChaining": "error",
        "noUnusedFunctionParameters": "error",
        "noUnusedImports": "error",
        "noUnusedLabels": "error",
        "noUnusedPrivateClassMembers": "warn",
        "noUnusedVariables": "off",
        "noVoidElementsWithChildren": "error",
        "noVoidTypeReturn": "off",
        "useExhaustiveDependencies": "error",
        "useHookAtTopLevel": "error",
        "useImportExtensions": "off",
        "useIsNan": "error",
        "useJsxKeyInIterable": "error",
        "useValidForDirection": "warn",
        "useValidTypeof": "error",
        "useYield": "error",
      },
      "nursery": {
        "noAwaitInLoop": "off",
        "noBitwiseOperators": "error",
        "noConstantBinaryExpression": "error",
        "noDestructuredProps": "warn",
        "noFloatingPromises": "warn",
        "noGlobalDirnameFilename": "error",
        "noImportantStyles": "warn",
        "noImportCycles": "error",
        "noNestedComponentDefinitions": "error",
        "noNoninteractiveElementInteractions": "error",
        "noProcessGlobal": "error",
        "noReactPropAssign": "error",
        "noRestrictedElements": "error",
        "noSecrets": "off",
        "noShadow": "error",
        "noTsIgnore": "warn",
        "noUnknownAtRule": "error",
        "noUnresolvedImports": "error",
        "noUnwantedPolyfillio": "error",
        "noUselessBackrefInRegex": "error",
        "noUselessEscapeInString": "error",
        "noUselessUndefined": "error",
        "useAdjacentGetterSetter": "error",
        "useConsistentObjectDefinition": {
          "fix": "safe",
          "level": "error",
          "options": {
            "syntax": "shorthand",
          },
        },
        "useConsistentResponse": "error",
        "useExhaustiveSwitchCases": "error",
        "useExplicitType": "off",
        "useExportsLast": "error",
        "useForComponent": "error",
        "useGoogleFontPreconnect": "off",
        "useIndexOf": "error",
        "useIterableCallbackReturn": "error",
        "useJsonImportAttribute": "error",
        "useNamedOperation": "error",
        "useNamingConvention": "error",
        "useNumericSeparators": "error",
        "useObjectSpread": "off",
        "useParseIntRadix": "error",
        "useSingleJsDocAsterisk": "error",
        "useSortedClasses": "error",
        "useSymbolDescription": "error",
        "useUniqueElementIds": "error",
      },
      "performance": {
        "noAccumulatingSpread": "error",
        "noBarrelFile": "off",
        "noDelete": "error",
        "noDynamicNamespaceImportAccess": "error",
        "noImgElement": "error",
        "noNamespaceImport": "error",
        "noReExportAll": "off",
        "useTopLevelRegex": "error",
      },
      "security": {
        "noBlankTarget": "error",
        "noDangerouslySetInnerHtml": "error",
        "noDangerouslySetInnerHtmlWithChildren": "error",
        "noGlobalEval": "error",
      },
      "style": {
        "noCommonJs": "error",
        "noDefaultExport": "warn",
        "noDescendingSpecificity": "error",
        "noDoneCallback": "error",
        "noEnum": "error",
        "noExportedImports": "error",
        "noHeadElement": "error",
        "noImplicitBoolean": "off",
        "noInferrableTypes": "error",
        "noNamespace": "error",
        "noNegationElse": "error",
        "noNestedTernary": "off",
        "noNonNullAssertion": "warn",
        "noParameterAssign": "error",
        "noParameterProperties": "off",
        "noProcessEnv": "error",
        "noRestrictedGlobals": "error",
        "noRestrictedImports": "error",
        "noRestrictedTypes": "error",
        "noShoutyConstants": "error",
        "noSubstr": "error",
        "noUnusedTemplateLiteral": "error",
        "noUselessElse": "error",
        "noValueAtRule": "error",
        "noYodaExpression": "error",
        "useArrayLiterals": "error",
        "useAsConstAssertion": "error",
        "useAtIndex": "info",
        "useBlockStatements": "error",
        "useCollapsedElseIf": "error",
        "useCollapsedIf": "error",
        "useComponentExportOnlyModules": "error",
        "useConsistentArrayType": "error",
        "useConsistentBuiltinInstantiation": "error",
        "useConsistentCurlyBraces": "error",
        "useConsistentMemberAccessibility": "error",
        "useConst": "error",
        "useDefaultParameterLast": "error",
        "useDefaultSwitchClause": "error",
        "useDeprecatedReason": "error",
        "useEnumInitializers": "error",
        "useExplicitLengthCheck": "error",
        "useExponentiationOperator": "error",
        "useExportType": "error",
        "useFilenamingConvention": "error",
        "useForOf": "off",
        "useFragmentSyntax": "error",
        "useImportType": "error",
        "useLiteralEnumMembers": "error",
        "useNamingConvention": {
          "level": "warn",
          "options": {
            "requireAscii": true,
            "strictCase": false,
          },
        },
        "useNodeAssertStrict": "error",
        "useNodejsImportProtocol": "error",
        "useNumberNamespace": "error",
        "useSelfClosingElements": "error",
        "useShorthandAssign": "error",
        "useShorthandFunctionType": "error",
        "useSingleVarDeclarator": "error",
        "useTemplate": "error",
        "useThrowNewError": "off",
        "useThrowOnlyError": "warn",
        "useTrimStartEnd": "error",
      },
      "suspicious": {
        "noApproximativeNumericConstant": "off",
        "noArrayIndexKey": "error",
        "noAssignInExpressions": "error",
        "noAsyncPromiseExecutor": "error",
        "noCatchAssign": "error",
        "noClassAssign": "error",
        "noCommentText": "error",
        "noCompareNegZero": "error",
        "noConfusingLabels": "error",
        "noConfusingVoidType": "off",
        "noConsole": "error",
        "noConstEnum": "error",
        "noControlCharactersInRegex": "error",
        "noDebugger": "error",
        "noDocumentCookie": "error",
        "noDocumentImportInPage": "error",
        "noDoubleEquals": "warn",
        "noDuplicateAtImportRules": "error",
        "noDuplicateCase": "error",
        "noDuplicateClassMembers": "error",
        "noDuplicateCustomProperties": "error",
        "noDuplicateElseIf": "error",
        "noDuplicateFields": "error",
        "noDuplicateFontNames": "error",
        "noDuplicateJsxProps": "error",
        "noDuplicateObjectKeys": "error",
        "noDuplicateParameters": "error",
        "noDuplicateProperties": "error",
        "noDuplicateSelectorsKeyframeBlock": "error",
        "noDuplicateTestHooks": "error",
        "noEmptyBlock": "error",
        "noEmptyBlockStatements": "warn",
        "noEmptyInterface": "error",
        "noEvolvingTypes": "error",
        "noExplicitAny": "error",
        "noExportsInTest": "error",
        "noExtraNonNullAssertion": "error",
        "noFallthroughSwitchClause": "error",
        "noFocusedTests": "error",
        "noFunctionAssign": "error",
        "noGlobalAssign": "error",
        "noGlobalIsFinite": "error",
        "noGlobalIsNan": "error",
        "noHeadImportInDocument": "error",
        "noImplicitAnyLet": "error",
        "noImportAssign": "error",
        "noImportantInKeyframe": "error",
        "noIrregularWhitespace": "error",
        "noLabelVar": "error",
        "noMisleadingCharacterClass": "error",
        "noMisleadingInstantiator": "warn",
        "noMisplacedAssertion": "error",
        "noMisrefactoredShorthandAssign": "error",
        "noOctalEscape": "error",
        "noPrototypeBuiltins": "error",
        "noReactSpecificProps": "error",
        "noRedeclare": "off",
        "noRedundantUseStrict": "error",
        "noSelfCompare": "error",
        "noShadowRestrictedNames": "error",
        "noShorthandPropertyOverrides": "warn",
        "noSkippedTests": "error",
        "noSparseArray": "error",
        "noSuspiciousSemicolonInJsx": "error",
        "noTemplateCurlyInString": "error",
        "noThenProperty": "error",
        "noUnsafeDeclarationMerging": "error",
        "noUnsafeNegation": "error",
        "noVar": "error",
        "noWith": "error",
        "useAdjacentOverloadSignatures": "error",
        "useAwait": "error",
        "useDefaultSwitchClauseLast": "error",
        "useErrorMessage": "error",
        "useGetterReturn": "error",
        "useGoogleFontDisplay": "off",
        "useGuardForIn": "error",
        "useIsArray": "error",
        "useNamespaceKeyword": "error",
        "useNumberToFixedDigitsArgument": "error",
        "useStrictMode": "error",
      },
    },
  },

  "overrides": [
    {
      "includes": ["**/*.ts"],
      "linter": {
        "rules": {
          "nursery": {
            "noUnresolvedImports": "off",
          },
        },
      },
    },
    {
      "includes": ["**/definitions/**/*.ts", "**/_/**/*.ts", "**/types/**/*.ts", "**/types.ts", "**/definition.ts"],
      "linter": {
        "rules": {
          "nursery": {
            "noShadow": "off",
          },
          "style": {
            "noNamespace": "off",
          },
          "suspicious": {
            "noEmptyInterface": "off",
          },
        },
      },
    },
    {
      "includes": ["**/plugin/**/*.ts", "**/plugins/**/*.ts", "**/plugin-system/**/*.ts"],
      "linter": {
        "rules": {
          "correctness": {
            "useHookAtTopLevel": "off",
          },
        },
      },
    },
  ],

  "root": true,
  "vcs": {
    "clientKind": "git",
    "defaultBranch": "development",
    "enabled": true,
    "useIgnoreFile": true,
  },
}
