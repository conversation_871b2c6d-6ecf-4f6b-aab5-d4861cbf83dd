{"author": "giveerr (https://github.com/giveerr)", "dependencies": {"@ozaco/std": "workspace:*"}, "devDependencies": {"@ozaco/cli": "workspace:*"}, "exports": {".": {"default": "./dist/index.js", "source": "./src/index.ts", "types": "./dist/index.d.ts"}, "./split": {"default": "./dist/split.js", "source": "./split/index.ts", "types": "./dist/split.d.ts"}}, "files": ["dist"], "homepage": "https://ozaco.com/", "name": "@ozaco/example", "private": true, "repository": {"type": "git", "url": "https://github.com/ozaco/ozaco.git"}, "splitBuilds": ["split"], "type": "module", "version": "0.0.0"}