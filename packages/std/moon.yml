language: "typescript"
type: "library"
platform: "bun"

tasks:
  watch:
    command: "bunx ozaco build -w"
    deps:
      - "cli:dev"
    inputs:
      - "src/**/*"
    outputs:
      - "dist/**"
    local: true
  dev:
    command: "bunx ozaco build"
    deps:
      - "cli:dev"
    inputs:
      - "src/**/*"
    outputs:
      - "dist/**"
  build:
    command: "bunx ozaco build -s -m prod"
    deps:
      - "cli:build"
    inputs:
      - "src/**/*"
    outputs:
      - "dist/**"
  test:
    command: "bun test"
    deps:
      - "std:build"
    inputs:
      - "tests/**/*"
    local: true
