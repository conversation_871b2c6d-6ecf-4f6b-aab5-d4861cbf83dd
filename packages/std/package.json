{"author": "giveerr (https://github.com/giveerr)", "bundleDependencies": ["picocolors", "type-fest"], "dependencies": {"picocolors": "^1.1.1", "type-fest": "^4.41.0"}, "devDependencies": {"@ozaco/cli": "workspace:*"}, "exports": {"./effects": {"default": "./dist/effects.js", "source": "./src/effects/index.ts", "types": "./dist/effects.d.ts"}, "./io": {"default": "./dist/io.js", "source": "./src/io/index.ts", "types": "./dist/io.d.ts"}, "./logger": {"default": "./dist/logger.js", "source": "./src/logger/index.ts", "types": "./dist/logger.d.ts"}, "./logger-file": {"default": "./dist/logger-file.js", "source": "./src/logger/file-transport/index.ts", "types": "./dist/logger-file.d.ts"}, "./parser": {"default": "./dist/parser.js", "source": "./src/parser/index.ts", "types": "./dist/parser.d.ts"}, "./plugin": {"default": "./dist/plugin.js", "source": "./src/plugin/index.ts", "types": "./dist/plugin.d.ts"}, "./results": {"default": "./dist/results.js", "source": "./src/results/index.ts", "types": "./dist/results.d.ts"}, "./shared": {"default": "./dist/shared.js", "source": "./src/shared/index.ts", "types": "./dist/shared.d.ts"}}, "files": ["dist"], "homepage": "https://ozaco.com/", "name": "@ozaco/std", "repository": {"type": "git", "url": "https://github.com/ozaco/ozaco.git"}, "type": "module", "version": "0.0.5"}