import { randomBytes } from 'node:crypto'

import { err, fromThrowable } from '../../results'

import { cryptoTags } from '../tags'

export const generateId = fromThrowable(
  (length = 16) => randomBytes(length).toString('hex'),
  e =>
    err(cryptoTags.get('unknown'), 'failed to generate id')
      .appendCause(cryptoTags.get('generate-ids'), cryptoTags.get('server'))
      .appendData(e),
) satisfies Std.Crypto.Api['generateId']
