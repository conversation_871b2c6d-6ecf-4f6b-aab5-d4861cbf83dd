import { err, fromThrowable } from '../../results'

import { cryptoTags } from '../tags'

// Pre-computed hex lookup table for maximum performance
const HEX_TABLE = Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'))

export const generateId = fromThrowable(
  (length = 16) => {
    
  },
  e =>
    err(cryptoTags.get('unknown'), 'failed to generate id')
      .appendCause(cryptoTags.get('generate-ids'), cryptoTags.get('web'))
      .appendData(e),
) satisfies Std.Crypto.Api['generateId']
