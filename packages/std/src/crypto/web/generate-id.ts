import { err, fromThrowable } from '../../results'

import { cryptoTags } from '../tags'

const HEX_TABLE = Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'))

export const generateId = fromThrowable(
  (length = 16) => {
    const array = new Uint8Array(length)
    window.crypto.getRandomValues(array)

    let hex = ''
    for (let i = 0; i < length; i++) {
      const byte = array[i]
      if (byte !== undefined) {
        hex += HEX_TABLE[byte]
      }
    }
    return hex
  },
  e =>
    err(cryptoTags.get('unknown'), 'failed to generate id')
      .appendCause(cryptoTags.get('generate-ids'), cryptoTags.get('web'))
      .appendData(e),
) satisfies Std.Crypto.Api['generateId']
