import { err, fromThrowable } from '../../results'

import { cryptoTags } from '../tags'

export const generateId = fromThrowable(
  (length = 16) => {
    const array = new Uint32Array(length / 2)

    window.crypto.getRandomValues(array)

    return Array.from(array, dec => `0${dec.toString(16)}`.slice(-2)).join('')
  },
  e =>
    err(cryptoTags.get('unknown'), 'failed to generate id')
      .appendCause(cryptoTags.get('generate-ids'), cryptoTags.get('web'))
      .appendData(e),
) satisfies Std.Crypto.Api['generateId']
