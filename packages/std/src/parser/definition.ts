import type { Fn } from '../shared'
import type { parserTags } from './tag'

declare global {
  namespace Std {
    // ------------ Errors ------------
    interface Error {
      'std/parser': typeof parserTags
    }

    namespace Parser {
      // ------------ Lexer ------------

      type LexerOptions = Std.Parser.LexerConfig[]

      interface Token {
        type: string
        value: string
        position: [number, number] | null
      }

      interface LexerConfig {
        type: string
        regexes: RegExp[]
        /**
         * Will match, by not add to token list.
         */
        ignore?: boolean
      }

      // ------------ Parser ------------

      type ParserOptions = [config?: Std.Parser.ParserConfig]

      interface ParserConfig {
        cursorTokenExcludes?: (token?: Std.Parser.Token) => boolean
      }

      interface ParserDebug {
        lexerStart: number
        lexerTime: number

        parserStart: number
        parserTime: number
      }

      interface MatchedToken extends Std.Parser.Token {
        $include: boolean
      }

      interface ChainOptions {
        name: string

        matchers: Fn<
          [token: Std.Parser.Token],
          Std.Result<
            Std.Parser.MatchedToken,
            'std/results.invalid-usage' | 'parser@0.0.0#grammar/doesnt-match',
            'parser@0.0.0#grammar/match'[]
          >
        >[]
      }

      type Ast = Std.Parser.Node[]

      interface Node<M = unknown> {
        type: string
        value: string
        tokens: Std.Parser.Token[]
        position: [number, number] | null

        meta: M

        childs: Std.Parser.Node[]
      }

      // ------------ Grammar ------------

      interface Grammar extends Std.Parser.ChainOptions {
        cursors: Map<number, number>
      }

      type Grammars = Std.Parser.Grammar[]
    }
  }
}
