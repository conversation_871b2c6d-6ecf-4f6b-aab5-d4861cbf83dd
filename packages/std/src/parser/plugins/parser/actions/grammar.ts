import { $fn, err } from '../../../../results'
import { isArray } from '../../../../shared'

import { parserPluginBase } from '../base'

export const grammarAction = parserPluginBase.direct('grammar', rawCtx => {
  const ctx = rawCtx.tag('exists').tag('doesnt-match').tag('match').tag('chain')

  const $grammars = ctx.$signal([] as Std.Parser.Grammars)

  const addGrammar = ctx.$fn('add-grammar', (data: Std.Parser.Grammar) => {
    const found = $grammars().find(grammar => grammar.name === data.name)

    if (found) {
      return err(ctx.tags.get('grammar/exists'), `grammar (${data.name}) already exists`)
    }

    return $grammars(grammars => {
      grammars.push(data)

      return grammars
    })
  })

  const match = (type: 'value' | 'type', data: string | string[], include = false) => {
    const datas = isArray(data) ? data : [data]

    const matcher =
      type === 'value'
        ? (token: Std.Parser.Token) => datas.includes(token.value)
        : (token: Std.Parser.Token) => datas.includes(token.type)

    return $fn(
      (token: Std.Parser.Token) =>
        matcher(token)
          ? (Object.assign({}, token, {
              $include: include,
            }) as Std.Parser.MatchedToken)
          : err(ctx.tags.get('grammar/doesnt-match'), 'token doesnt match'),
      ctx.tags.get('grammar/match'),
    )
  }

  const chain = ({ name, matchers }: Std.Parser.ChainOptions) => {}

  return ctx.apply({
    $grammars,
    addGrammar,
    chain,
    match,
  })
})
