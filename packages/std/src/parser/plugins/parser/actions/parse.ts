import { parserPluginBase } from '../base'
import { contextAction } from './context'

export const parseAction = parserPluginBase.direct('parse', ctx => {
  const parse = ctx.$safe('parse', function* (text: string, _cursorIndex: number | null = null) {
    const context = yield* ctx.$peek(contextAction)
    const lexer = yield* ctx.$get('lexer')

    const $ast = ctx.$signal([] as Std.Parser.Ast)
    const $debug = ctx.$signal({} as Std.Parser.ParserDebug)
    const $tokens = ctx.$signal([] as Std.Parser.Token[])

    {
      const now = performance.now()

      $debug({
        lexerStart: now,
        lexerTime: 0,

        parserStart: now,
        parserTime: 0,
      })
    }

    $tokens(yield* lexer.tokenize(text))

    $debug(debug => {
      debug.lexerTime = performance.now() - debug.lexerStart

      return debug
    })

    $debug(debug => {
      debug.parserTime = performance.now() - debug.parserStart

      return debug
    })

    return {
      $ast,
      $debug,
      $tokens,
    }
  })

  return ctx.apply({
    parse,
  })
})
