import type { BlobType } from '../types/common'

export const tailCallOptimize = <T>(f: T): T => {
  let value: BlobType
  let active = false
  const accumulated: BlobType[] = []
  return function accumulator(this: BlobType) {
    // biome-ignore lint/complexity/noArguments: Redundant
    accumulated.push(arguments)
    if (!active) {
      active = true
      while (accumulated.length > 0) {
        value = (f as BlobType).apply(this, accumulated.shift())
      }
      active = false
      return value
    }
  } as BlobType
}
