import { generateId } from './packages/std/src/crypto/server/generate-id'

// Test the optimized generateId function
console.log('=== Crypto ID Generation Test ===')

// Test default length (16 bytes = 32 hex characters)
const id1 = generateId()
console.log('Default (16 bytes):', id1.isOk() ? id1.value : id1.error)
console.log('Length:', id1.isOk() ? id1.value.length : 'N/A')

// Test custom length (8 bytes = 16 hex characters)
const id2 = generateId(8)
console.log('Custom (8 bytes):', id2.isOk() ? id2.value : id2.error)
console.log('Length:', id2.isOk() ? id2.value.length : 'N/A')

// Test custom length (32 bytes = 64 hex characters)
const id3 = generateId(32)
console.log('Custom (32 bytes):', id3.isOk() ? id3.value : id3.error)
console.log('Length:', id3.isOk() ? id3.value.length : 'N/A')

// Test hex format (should only contain 0-9 and a-f)
const id4 = generateId(16)
if (id4.isOk()) {
  const isValidHex = /^[0-9a-f]+$/.test(id4.value)
  console.log('Valid hex format:', isValidHex)
  console.log('Sample ID:', id4.value)
}
