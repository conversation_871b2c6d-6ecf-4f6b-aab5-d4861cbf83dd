{"author": "giveerr (https://github.com/giveerr)", "bin": {"ozaco": "./command"}, "bundleDependencies": ["clerc", "acorn", "acorn-loose"], "dependencies": {"acorn": "^8.15.0", "acorn-loose": "^8.5.1", "clerc": "^0.44.0"}, "description": "All in one cli for ozaco", "devDependencies": {"type-fest": "^4.41.0", "typescript": "^5.8.3"}, "exports": {".": {"default": "./dist/index.js", "source": "./src/index.ts", "types": "./dist/index.d.ts"}, "./builder": {"default": "./dist/packages/builder.js", "source": "./packages/builder/index.ts", "types": "./dist/packages/builder.d.ts"}, "./main": {"default": "./dist/main.js", "source": "./src/main.ts", "types": "./dist/main.d.ts"}}, "files": ["dist", "./command"], "homepage": "https://ozaco.com/", "name": "@ozaco/cli", "peerDependencies": {"typescript": ">= 5.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "repository": {"type": "git", "url": "https://github.com/ozaco/ozaco.git"}, "type": "module", "version": "0.0.11"}