language: "typescript"
type: "library"
platform: "bun"

tasks:
  watch:
    command: "bunx ozaco build -w -s -f cjs -t node"
    deps:
      - "cli:dev"
      - "std:dev"
    inputs:
      - "src/**/*"
    outputs:
      - "dist/**"
    local: true
  dev:
    command: "bunx ozaco build -f cjs -t node"
    deps:
      - "std:dev"
    inputs:
      - "src/**/*"
    outputs:
      - "dist/**"
  build:
    command: "bunx ozaco build -s -f cjs -t node -m prod"
    deps:
      - "std:build"
    inputs:
      - "src/**/*"
    outputs:
      - "dist/**"
