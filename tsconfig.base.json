{
  "compilerOptions": {
    /* Moonrepo */
    "allowArbitraryExtensions": true,
    "allowImportingTsExtensions": false,
    "allowJs": true,
    /* Moonrepo */
    "composite": true,
    "declaration": true,
    "declarationMap": true,
    "downlevelIteration": true,
    "emitDeclarationOnly": true,
    "emitDecoratorMetadata": true,

    /* Base Options: */
    "esModuleInterop": true,
    "exactOptionalPropertyTypes": true,
    "experimentalDecorators": true,

    "importHelpers": true,
    "incremental": true,
    "isolatedModules": true,
    "jsx": "react-jsx",
    "lib": ["esnext"],
    "module": "esnext",
    "moduleDetection": "force",

    "moduleResolution": "Bundler",
    "noEmitOnError": true,
    "noImplicitAny": true,
    "noUncheckedIndexedAccess": true,
    "resolveJsonModule": true,
    "skipLibCheck": true,

    /* Strictness */
    "strict": true,
    "strictNullChecks": true,
    "target": "esnext",
    "verbatimModuleSyntax": true,
  },
  "exclude": ["**/dist/**"],
}
